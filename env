# Database connection string
DATABASE_URI=postgres://ded:developer@127.0.0.1:5432/ded_db
# Or use a PG connection string
#DATABASE_URI=postgresql://127.0.0.1:5432/your-database-name
# Used to encrypt JWT tokens
PAYLOAD_SECRET=f70d0aebcd4e8f93e4c1f31f
# Used to configure CORS, format links and more. No trailing slash
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
# Secret used to authenticate cron jobs
CRON_SECRET=YOUR_CRON_SECRET_HERE
# Used to validate preview requests
PREVIEW_SECRET=YOUR_SECRET_HERE
# Added by Payload

SMTP_HOST=smtp.gmail.com
SMTP_PORT=465
SMTP_SECURE=true
SMTP_USER=<EMAIL>
SMTP_PASS=zjra rilh zaqs qpxs
FROM_EMAIL=<EMAIL>
FROM_NAME=IKIA 2025