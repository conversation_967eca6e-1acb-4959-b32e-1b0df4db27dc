<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Partners & Sponsors - IKIA Conference 2025</title>
    <style>
        @font-face {
            font-family: 'Acquire';
            src: url('./aquire-font/Aquire-BW0ox.otf') format('opentype');
            font-weight: normal;
        }
        
        @font-face {
            font-family: 'Acquire';
            src: url('./aquire-font/AquireBold-8Ma60.otf') format('opentype');
            font-weight: bold;
        }
        
        @font-face {
            font-family: 'Acquire';
            src: url('./aquire-font/AquireLight-YzE0o.otf') format('opentype');
            font-weight: 300;
        }

        :root {
            --primary-brown: #7E2518;
            --muted-green: #4a7c59;
            --blue: #81B1DB;
            --yellow-ochre: #E8B32C;
            --sienna: #C86E36;
            --light-cream: #fdf9f4;
            --warm-gray: #f5f2ed;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Myriad Pro', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
        }

        /* Navigation */
        .navbar {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--sienna) 100%);
            padding: 1rem 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }

        .logo {
            color: white;
            font-family: 'Acquire', sans-serif;
            font-size: 1.8rem;
            font-weight: bold;
            text-decoration: none;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-menu a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            font-family: 'Myriad Pro', Arial, sans-serif;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: var(--yellow-ochre);
        }

        .sponsor-btn {
            background: var(--yellow-ochre);
            color: var(--primary-brown);
            padding: 0.5rem 1.5rem;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            font-family: 'Myriad Pro', Arial, sans-serif;
            transition: transform 0.3s;
        }

        .sponsor-btn:hover {
            transform: translateY(-2px);
        }

        /* Hero Section - Split Layout */
        .hero {
            display: flex;
            flex-wrap: wrap;
            min-height: 70vh;
            padding-top: 80px;
            position: relative;
            overflow: hidden;
        }

        .hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--primary-brown) 50%, var(--light-cream) 50%);
            z-index: -1;
        }

        .hero-left, .hero-right {
            flex: 1 1 50%;
            padding: 4rem 2rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .hero-left {
            color: white;
            position: relative;
        }

        .hero-left h1 {
            font-family: 'Acquire', sans-serif;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            line-height: 1.2;
        }

        .hero-left .subtitle {
            font-family: 'Myriad Pro', Arial, sans-serif;
            font-size: 1.1rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2.5rem;
            max-width: 400px;
        }

        .hero-right {
            background: var(--light-cream);
            color: var(--primary-brown);
            text-align: center;
            position: relative;
        }

        .hero-right::before {
            content: '';
            position: absolute;
            top: 20%;
            left: 10%;
            right: 10%;
            bottom: 20%;
            background: url('./African World Heritage Day.jpeg') center/cover;
            border-radius: 20px;
            opacity: 0.3;
        }

        .hero-cta {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;
            z-index: 2;
            position: relative;
        }

        .btn-primary {
            background: var(--muted-green);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 30px;
            font-size: 1rem;
            font-weight: bold;
            font-family: 'Myriad Pro', Arial, sans-serif;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background: #3a5f47;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            color: white;
            padding: 1rem 2rem;
            border: 2px solid white;
            border-radius: 30px;
            font-size: 1rem;
            font-weight: bold;
            font-family: 'Myriad Pro', Arial, sans-serif;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s;
        }

        .btn-secondary:hover {
            background: white;
            color: var(--primary-brown);
        }

        /* Partners Section */
        .partners {
            padding: 5rem 0;
            background: var(--warm-gray);
        }

        .partners-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .partner-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 3rem;
            flex-wrap: wrap;
        }

        .partner-logo {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-brown), var(--sienna));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1rem;
            font-family: 'Acquire', sans-serif;
            text-align: center;
            transition: transform 0.3s;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .partner-logo:hover {
            transform: scale(1.1);
        }

        /* Tiers Section */
        .tiers {
            padding: 6rem 0;
            background: white;
        }

        .tiers-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .tiers h2 {
            font-family: 'Acquire', sans-serif;
            font-size: 2.2rem;
            color: var(--primary-brown);
            margin-bottom: 3rem;
        }

        .tiers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .tier-card {
            background: white;
            border-radius: 15px;
            padding: 2.5rem 2rem;
            text-align: center;
            border: 2px solid transparent;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }

        .tier-card:hover {
            transform: translateY(-5px);
            border-color: var(--muted-green);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }

        .tier-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, var(--primary-brown), var(--yellow-ochre));
        }

        .tier-title {
            font-family: 'Acquire', sans-serif;
            font-size: 1.5rem;
            color: var(--primary-brown);
            margin-bottom: 1rem;
        }

        .tier-price {
            font-family: 'Acquire', sans-serif;
            font-size: 2rem;
            color: var(--muted-green);
            margin-bottom: 1.5rem;
            font-weight: bold;
        }

        .tier-features {
            list-style: none;
            margin-bottom: 2rem;
            text-align: left;
        }

        .tier-features li {
            padding: 0.5rem 0;
            color: #666;
            font-family: 'Myriad Pro', Arial, sans-serif;
            position: relative;
            padding-left: 1.5rem;
        }

        .tier-features li::before {
            content: '✓';
            color: var(--muted-green);
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .hero {
                flex-direction: column;
            }

            .hero::before {
                background: var(--primary-brown);
            }

            .hero-left, .hero-right {
                flex: 1 1 100%;
                padding: 2rem;
            }

            .hero-right {
                background: var(--primary-brown);
                color: white;
            }

            .hero-right::before {
                display: none;
            }

            .hero-left h1 {
                font-size: 2.2rem;
            }

            .hero-left .subtitle {
                font-size: 1.1rem;
            }

            .hero-cta {
                gap: 1rem;
                justify-content: center;
            }

            .partner-logos {
                gap: 2rem;
            }

            .partner-logo {
                width: 80px;
                height: 80px;
                font-size: 0.9rem;
            }

            .tiers h2 {
                font-size: 1.8rem;
            }

            .tiers-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .logo {
                font-size: 1.4rem;
            }
            
            .hero-container h1 {
                font-size: 1.8rem;
            }
            
            .btn-primary, .btn-secondary {
                padding: 0.8rem 1.5rem;
                font-size: 0.9rem;
            }
            
            .partner-logo {
                width: 70px;
                height: 70px;
                font-size: 0.8rem;
            }
        }

        /* Countdown Banner */
        .countdown-banner {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--sienna) 100%);
            padding: 4rem 0;
            text-align: center;
            color: white;
        }

        .countdown-banner h2 {
            font-family: 'Acquire', sans-serif;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .countdown-banner p {
            font-family: 'Myriad Pro', Arial, sans-serif;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .countdown {
            display: flex;
            gap: 2rem;
            justify-content: center;
            margin-bottom: 2rem;
        }

        .countdown-item {
            text-align: center;
            color: white;
        }

        .countdown-number {
            font-family: 'Acquire', sans-serif;
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }

        .countdown-label {
            font-family: 'Myriad Pro', Arial, sans-serif;
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Inquiries Section */
        .inquiries {
            padding: 6rem 0;
            background: var(--warm-gray);
        }

        .inquiries-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 2rem;
            text-align: center;
        }

        .inquiries h2 {
            font-family: 'Acquire', sans-serif;
            font-size: 2.2rem;
            color: var(--primary-brown);
            margin-bottom: 3rem;
        }

        .inquiry-form {
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: left;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-brown);
            font-weight: 500;
            font-family: 'Myriad Pro', Arial, sans-serif;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-family: 'Myriad Pro', Arial, sans-serif;
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: var(--muted-green);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 120px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }

        .submit-btn {
            background: var(--muted-green);
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 30px;
            font-size: 1rem;
            font-weight: bold;
            font-family: 'Myriad Pro', Arial, sans-serif;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
            margin-top: 1rem;
        }

        .submit-btn:hover {
            background: #3a5f47;
            transform: translateY(-2px);
        }

        /* Footer */
        .footer {
            background: var(--primary-brown);
            color: white;
            padding: 3rem 0 1rem;
        }

        .footer-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
        }

        .footer-section h3 {
            font-family: 'Acquire', sans-serif;
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: var(--yellow-ochre);
        }

        .footer-section p, .footer-section a {
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            line-height: 1.6;
            font-family: 'Myriad Pro', Arial, sans-serif;
        }

        .footer-section a:hover {
            color: var(--yellow-ochre);
        }

        .footer-links {
            list-style: none;
        }

        .footer-links li {
            margin-bottom: 0.5rem;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255,255,255,0.2);
            margin-top: 2rem;
            padding-top: 1rem;
            text-align: center;
            color: rgba(255,255,255,0.6);
            font-family: 'Myriad Pro', Arial, sans-serif;
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 768px) {
            .countdown {
                gap: 1rem;
            }

            .countdown-number {
                font-size: 2rem;
            }

            .countdown-banner h2 {
                font-size: 1.6rem;
            }

            .inquiries h2 {
                font-size: 1.8rem;
            }

            .inquiry-form {
                padding: 2rem;
            }

            .form-row {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .countdown {
                gap: 0.5rem;
            }

            .countdown-number {
                font-size: 1.5rem;
            }

            .inquiry-form {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="index.html" class="logo">IKIA</a>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#program">Program</a></li>
                <li><a href="#speakers">Speakers</a></li>
                <li><a href="#exhibitors">Exhibitors</a></li>
                <li><a href="partners.html">Partners</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <a href="#sponsor" class="sponsor-btn">Become a Sponsor</a>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-left">
            <h1>Partners & Sponsors</h1>
            <p class="subtitle">Empowering collaboration through culture and heritage</p>

            <div class="hero-cta">
                <a href="#sponsor" class="btn-primary">Become a Sponsor</a>
                <a href="#prospectus" class="btn-secondary">Download Prospectus</a>
            </div>
        </div>
        <div class="hero-right">
            <!-- Background image will be applied via CSS -->
        </div>
    </section>

    <!-- Partners Section -->
    <section class="partners">
        <div class="partners-container">
            <div class="partner-logos">
                <div class="partner-logo">KENYA</div>
                <div class="partner-logo">NMK</div>
                <div class="partner-logo">KIPI</div>
                <div class="partner-logo">VISION 2030</div>
                <div class="partner-logo">BETA</div>
            </div>
        </div>
    </section>

    <!-- Sponsorship Tiers Section -->
    <section class="tiers">
        <div class="tiers-container">
            <h2>Sponsorship Tiers & Benefits</h2>
            <div class="tiers-grid">
                <div class="tier-card">
                    <h3 class="tier-title">Platinum Sponsor</h3>
                    <div class="tier-price">$50,000</div>
                    <ul class="tier-features">
                        <li>Prime exhibition space</li>
                        <li>Keynote speaking opportunity</li>
                        <li>Logo on all marketing materials</li>
                        <li>VIP networking access</li>
                        <li>Media interview opportunities</li>
                        <li>Custom branding opportunities</li>
                    </ul>
                    <a href="#contact" class="btn-primary">Get Started</a>
                </div>
                
                <div class="tier-card">
                    <h3 class="tier-title">Gold Sponsor</h3>
                    <div class="tier-price">$25,000</div>
                    <ul class="tier-features">
                        <li>Premium exhibition space</li>
                        <li>Panel discussion participation</li>
                        <li>Logo on conference materials</li>
                        <li>Networking event access</li>
                        <li>Social media promotion</li>
                        <li>Conference proceedings inclusion</li>
                    </ul>
                    <a href="#contact" class="btn-primary">Get Started</a>
                </div>
                
                <div class="tier-card">
                    <h3 class="tier-title">Silver Sponsor</h3>
                    <div class="tier-price">$10,000</div>
                    <ul class="tier-features">
                        <li>Standard exhibition space</li>
                        <li>Workshop hosting opportunity</li>
                        <li>Logo on website</li>
                        <li>Welcome reception access</li>
                        <li>Digital marketing inclusion</li>
                        <li>Attendee list access</li>
                    </ul>
                    <a href="#contact" class="btn-primary">Get Started</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Countdown Banner -->
    <section class="countdown-banner">
        <div class="partners-container">
            <h2>Countdown to Kenya's First IKIA Investment Conference</h2>
            <p>Don't miss this historic opportunity to be part of Kenya's indigenous knowledge revolution</p>
            <div class="countdown" id="countdown">
                <div class="countdown-item">
                    <span class="countdown-number" id="days">00</span>
                    <span class="countdown-label">Days</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="hours">00</span>
                    <span class="countdown-label">Hours</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="minutes">00</span>
                    <span class="countdown-label">Minutes</span>
                </div>
                <div class="countdown-item">
                    <span class="countdown-number" id="seconds">00</span>
                    <span class="countdown-label">Seconds</span>
                </div>
            </div>
            <a href="#sponsor" class="btn-primary">Secure Your Partnership</a>
        </div>
    </section>

    <!-- Sponsorship Inquiries Section -->
    <section class="inquiries" id="sponsor">
        <div class="inquiries-container">
            <h2>Sponsorship Inquiries</h2>
            <form class="inquiry-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="company">Company</label>
                        <input type="text" id="company" name="company" required>
                    </div>
                    <div class="form-group">
                        <label for="contact">Contact Person</label>
                        <input type="text" id="contact" name="contact" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="email">Email</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone Number</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="tier">Sponsorship Tier Interest</label>
                    <select id="tier" name="tier" required>
                        <option value="">Select a tier</option>
                        <option value="platinum">Platinum Sponsor - $50,000</option>
                        <option value="gold">Gold Sponsor - $25,000</option>
                        <option value="silver">Silver Sponsor - $10,000</option>
                        <option value="custom">Custom Package</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="message">Message</label>
                    <textarea id="message" name="message" placeholder="Tell us about your organization and sponsorship goals..."></textarea>
                </div>

                <button type="submit" class="submit-btn">Submit Inquiry</button>
            </form>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="footer-container">
            <div class="footer-section">
                <h3>IKIA Conference 2025</h3>
                <p>First International Investment Conference & Trade Fair on Kenya's Indigenous Knowledge Intellectual Assets</p>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#program">Program</a></li>
                    <li><a href="#speakers">Speakers</a></li>
                    <li><a href="partners.html">Partners</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Contact Info</h3>
                <p>Email: <EMAIL></p>
                <p>Phone: +254 700 000 000</p>
                <p>Address: Nairobi, Kenya</p>
            </div>
        </div>

        <div class="footer-bottom">
            <p>&copy; 2025 IKIA Conference. All rights reserved. | Privacy Policy | Terms of Service</p>
        </div>
    </footer>

    <script>
        // Countdown Timer
        function updateCountdown() {
            const eventDate = new Date('2025-09-30T09:00:00').getTime();
            const now = new Date().getTime();
            const timeLeft = eventDate - now;

            const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24));
            const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

            document.getElementById('days').textContent = days.toString().padStart(2, '0');
            document.getElementById('hours').textContent = hours.toString().padStart(2, '0');
            document.getElementById('minutes').textContent = minutes.toString().padStart(2, '0');
            document.getElementById('seconds').textContent = seconds.toString().padStart(2, '0');

            if (timeLeft < 0) {
                document.getElementById('countdown').innerHTML = '<h2>Conference Started!</h2>';
            }
        }

        // Update countdown every second
        setInterval(updateCountdown, 1000);
        updateCountdown();

        // Form submission handler
        document.querySelector('.inquiry-form').addEventListener('submit', function(e) {
            e.preventDefault();
            alert('Thank you for your inquiry! We will contact you within 24 hours.');
        });

        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
